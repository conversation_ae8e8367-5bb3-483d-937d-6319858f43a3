{"version": 3, "names": ["_gensync", "data", "require", "asyncGeneratorStep", "n", "t", "e", "r", "o", "a", "c", "i", "u", "value", "done", "Promise", "resolve", "then", "_asyncToGenerator", "arguments", "apply", "_next", "_throw", "runGenerator", "gens<PERSON>", "item", "isAsync", "exports", "sync", "errback", "cb", "maybe<PERSON><PERSON>", "fn", "message", "args", "result", "isThenable", "Error", "async", "<PERSON><PERSON><PERSON>", "_ref", "_x", "forwardAsync", "action", "g", "kind", "adapted", "onFirstPause", "name", "arity", "firstPause", "completed", "err", "waitFor", "x", "_ref2", "_x2", "val"], "sources": ["../../src/gensync-utils/async.ts"], "sourcesContent": ["import gensync, { type <PERSON>sync, type Handler, type Callback } from \"gensync\";\n\ntype MaybePromise<T> = T | Promise<T>;\n\nconst runGenerator: {\n  sync<Return>(gen: <PERSON><PERSON><Return>): Return;\n  async<Return>(gen: <PERSON>ler<Return>): Promise<Return>;\n  errback<Return>(gen: <PERSON><PERSON><Return>, cb: Callback<Return>): void;\n} = gensync(function* (item: Handler<any>): Handler<any> {\n  return yield* item;\n});\n\n// This Gensync returns true if the current execution context is\n// asynchronous, otherwise it returns false.\nexport const isAsync = gensync({\n  sync: () => false,\n  errback: cb => cb(null, true),\n});\n\n// This function wraps any functions (which could be either synchronous or\n// asynchronous) with a Gensync. If the wrapped function returns a promise\n// but the current execution context is synchronous, it will throw the\n// provided error.\n// This is used to handle user-provided functions which could be asynchronous.\nexport function maybeAsync<Args extends unknown[], Return>(\n  fn: (...args: Args) => Return,\n  message: string,\n): Gensync<Args, Return> {\n  return gensync({\n    sync(...args) {\n      const result = fn.apply(this, args);\n      if (isThenable(result)) throw new Error(message);\n      return result;\n    },\n    async(...args) {\n      return Promise.resolve(fn.apply(this, args));\n    },\n  });\n}\n\nconst withKind = gensync({\n  sync: cb => cb(\"sync\"),\n  async: async cb => cb(\"async\"),\n}) as <T>(cb: (kind: \"sync\" | \"async\") => MaybePromise<T>) => Handler<T>;\n\n// This function wraps a generator (or a Gensync) into another function which,\n// when called, will run the provided generator in a sync or async way, depending\n// on the execution context where this forwardAsync function is called.\n// This is useful, for example, when passing a callback to a function which isn't\n// aware of gensync, but it only knows about synchronous and asynchronous functions.\n// An example is cache.using, which being exposed to the user must be as simple as\n// possible:\n//     yield* forwardAsync(gensyncFn, wrappedFn =>\n//       cache.using(x => {\n//         // Here we don't know about gensync. wrappedFn is a\n//         // normal sync or async function\n//         return wrappedFn(x);\n//       })\n//     )\nexport function forwardAsync<Args extends unknown[], Return>(\n  action: (...args: Args) => Handler<Return>,\n  cb: (\n    adapted: (...args: Args) => MaybePromise<Return>,\n  ) => MaybePromise<Return>,\n): Handler<Return> {\n  const g = gensync(action);\n  return withKind(kind => {\n    const adapted = g[kind];\n    return cb(adapted);\n  });\n}\n\n// If the given generator is executed asynchronously, the first time that it\n// is paused (i.e. When it yields a gensync generator which can't be run\n// synchronously), call the \"firstPause\" callback.\nexport const onFirstPause = gensync<\n  [gen: Handler<unknown>, firstPause: () => void],\n  unknown\n>({\n  name: \"onFirstPause\",\n  arity: 2,\n  sync: function (item) {\n    return runGenerator.sync(item);\n  },\n  errback: function (item, firstPause, cb) {\n    let completed = false;\n\n    runGenerator.errback(item, (err, value) => {\n      completed = true;\n      cb(err, value);\n    });\n\n    if (!completed) {\n      firstPause();\n    }\n  },\n}) as <T>(gen: Handler<T>, firstPause: () => void) => Handler<T>;\n\n// Wait for the given promise to be resolved\nexport const waitFor = gensync({\n  sync: x => x,\n  async: async x => x,\n}) as <T>(p: T | Promise<T>) => Handler<T>;\n\nexport function isThenable<T = any>(val: any): val is PromiseLike<T> {\n  return (\n    !!val &&\n    (typeof val === \"object\" || typeof val === \"function\") &&\n    !!val.then &&\n    typeof val.then === \"function\"\n  );\n}\n"], "mappings": ";;;;;;;;;;AAAA,SAAAA,SAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,QAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAA6E,SAAAE,mBAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,cAAAC,CAAA,GAAAP,CAAA,CAAAK,CAAA,EAAAC,CAAA,GAAAE,CAAA,GAAAD,CAAA,CAAAE,KAAA,WAAAT,CAAA,gBAAAE,CAAA,CAAAF,CAAA,KAAAO,CAAA,CAAAG,IAAA,GAAAT,CAAA,CAAAO,CAAA,IAAAG,OAAA,CAAAC,OAAA,CAAAJ,CAAA,EAAAK,IAAA,CAAAV,CAAA,EAAAC,CAAA;AAAA,SAAAU,kBAAAd,CAAA,6BAAAC,CAAA,SAAAC,CAAA,GAAAa,SAAA,aAAAJ,OAAA,WAAAR,CAAA,EAAAC,CAAA,QAAAC,CAAA,GAAAL,CAAA,CAAAgB,KAAA,CAAAf,CAAA,EAAAC,CAAA,YAAAe,MAAAjB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,UAAAlB,CAAA,cAAAkB,OAAAlB,CAAA,IAAAD,kBAAA,CAAAM,CAAA,EAAAF,CAAA,EAAAC,CAAA,EAAAa,KAAA,EAAAC,MAAA,WAAAlB,CAAA,KAAAiB,KAAA;AAI7E,MAAME,YAIL,GAAGC,SAAMA,CAAC,CAAC,WAAWC,IAAkB,EAAgB;EACvD,OAAO,OAAOA,IAAI;AACpB,CAAC,CAAC;AAIK,MAAMC,OAAO,GAAAC,OAAA,CAAAD,OAAA,GAAGF,SAAMA,CAAC,CAAC;EAC7BI,IAAI,EAAEA,CAAA,KAAM,KAAK;EACjBC,OAAO,EAAEC,EAAE,IAAIA,EAAE,CAAC,IAAI,EAAE,IAAI;AAC9B,CAAC,CAAC;AAOK,SAASC,UAAUA,CACxBC,EAA6B,EAC7BC,OAAe,EACQ;EACvB,OAAOT,SAAMA,CAAC,CAAC;IACbI,IAAIA,CAAC,GAAGM,IAAI,EAAE;MACZ,MAAMC,MAAM,GAAGH,EAAE,CAACZ,KAAK,CAAC,IAAI,EAAEc,IAAI,CAAC;MACnC,IAAIE,UAAU,CAACD,MAAM,CAAC,EAAE,MAAM,IAAIE,KAAK,CAACJ,OAAO,CAAC;MAChD,OAAOE,MAAM;IACf,CAAC;IACDG,KAAKA,CAAC,GAAGJ,IAAI,EAAE;MACb,OAAOnB,OAAO,CAACC,OAAO,CAACgB,EAAE,CAACZ,KAAK,CAAC,IAAI,EAAEc,IAAI,CAAC,CAAC;IAC9C;EACF,CAAC,CAAC;AACJ;AAEA,MAAMK,QAAQ,GAAGf,SAAMA,CAAC,CAAC;EACvBI,IAAI,EAAEE,EAAE,IAAIA,EAAE,CAAC,MAAM,CAAC;EACtBQ,KAAK;IAAA,IAAAE,IAAA,GAAAtB,iBAAA,CAAE,WAAMY,EAAE;MAAA,OAAIA,EAAE,CAAC,OAAO,CAAC;IAAA;IAAA,gBAA9BQ,KAAKA,CAAAG,EAAA;MAAA,OAAAD,IAAA,CAAApB,KAAA,OAAAD,SAAA;IAAA;EAAA;AACP,CAAC,CAAuE;AAgBjE,SAASuB,YAAYA,CAC1BC,MAA0C,EAC1Cb,EAEyB,EACR;EACjB,MAAMc,CAAC,GAAGpB,SAAMA,CAAC,CAACmB,MAAM,CAAC;EACzB,OAAOJ,QAAQ,CAACM,IAAI,IAAI;IACtB,MAAMC,OAAO,GAAGF,CAAC,CAACC,IAAI,CAAC;IACvB,OAAOf,EAAE,CAACgB,OAAO,CAAC;EACpB,CAAC,CAAC;AACJ;AAKO,MAAMC,YAAY,GAAApB,OAAA,CAAAoB,YAAA,GAAGvB,SAAMA,CAAC,CAGjC;EACAwB,IAAI,EAAE,cAAc;EACpBC,KAAK,EAAE,CAAC;EACRrB,IAAI,EAAE,SAAAA,CAAUH,IAAI,EAAE;IACpB,OAAOF,YAAY,CAACK,IAAI,CAACH,IAAI,CAAC;EAChC,CAAC;EACDI,OAAO,EAAE,SAAAA,CAAUJ,IAAI,EAAEyB,UAAU,EAAEpB,EAAE,EAAE;IACvC,IAAIqB,SAAS,GAAG,KAAK;IAErB5B,YAAY,CAACM,OAAO,CAACJ,IAAI,EAAE,CAAC2B,GAAG,EAAEvC,KAAK,KAAK;MACzCsC,SAAS,GAAG,IAAI;MAChBrB,EAAE,CAACsB,GAAG,EAAEvC,KAAK,CAAC;IAChB,CAAC,CAAC;IAEF,IAAI,CAACsC,SAAS,EAAE;MACdD,UAAU,CAAC,CAAC;IACd;EACF;AACF,CAAC,CAA+D;AAGzD,MAAMG,OAAO,GAAA1B,OAAA,CAAA0B,OAAA,GAAG7B,SAAMA,CAAC,CAAC;EAC7BI,IAAI,EAAE0B,CAAC,IAAIA,CAAC;EACZhB,KAAK;IAAA,IAAAiB,KAAA,GAAArC,iBAAA,CAAE,WAAMoC,CAAC;MAAA,OAAIA,CAAC;IAAA;IAAA,gBAAnBhB,KAAKA,CAAAkB,GAAA;MAAA,OAAAD,KAAA,CAAAnC,KAAA,OAAAD,SAAA;IAAA;EAAA;AACP,CAAC,CAAyC;AAEnC,SAASiB,UAAUA,CAAUqB,GAAQ,EAAyB;EACnE,OACE,CAAC,CAACA,GAAG,KACJ,OAAOA,GAAG,KAAK,QAAQ,IAAI,OAAOA,GAAG,KAAK,UAAU,CAAC,IACtD,CAAC,CAACA,GAAG,CAACxC,IAAI,IACV,OAAOwC,GAAG,CAACxC,IAAI,KAAK,UAAU;AAElC;AAAC", "ignoreList": []}