{"name": "picomatch", "description": "Blazing fast and accurate glob matcher written in JavaScript, with no dependencies and full support for standard and extended Bash glob features, including braces, extglobs, POSIX brackets, and regular expressions.", "version": "4.0.2", "homepage": "https://github.com/micromatch/picomatch", "author": "<PERSON> (https://github.com/jonschlinkert)", "funding": "https://github.com/sponsors/jonschlinkert", "repository": "micromatch/picomatch", "bugs": {"url": "https://github.com/micromatch/picomatch/issues"}, "license": "MIT", "files": ["index.js", "posix.js", "lib"], "sideEffects": false, "main": "index.js", "engines": {"node": ">=12"}, "scripts": {"lint": "eslint --cache --cache-location node_modules/.cache/.eslintcache --report-unused-disable-directives --ignore-path .gitignore .", "mocha": "mocha --reporter dot", "test": "npm run lint && npm run mocha", "test:ci": "npm run test:cover", "test:cover": "nyc npm run mocha"}, "devDependencies": {"eslint": "^8.57.0", "fill-range": "^7.0.1", "gulp-format-md": "^2.0.0", "mocha": "^10.4.0", "nyc": "^15.1.0", "time-require": "github:jonschlink<PERSON>/time-require"}, "keywords": ["glob", "match", "picomatch"], "nyc": {"reporter": ["html", "lcov", "text-summary"]}, "verb": {"toc": {"render": true, "method": "preWrite", "maxdepth": 3}, "layout": "empty", "tasks": ["readme"], "plugins": ["gulp-format-md"], "lint": {"reflinks": true}, "related": {"list": ["braces", "micromatch"]}, "reflinks": ["braces", "expand-brackets", "extglob", "fill-range", "micromatch", "minimatch", "nanomatch", "picomatch"]}}