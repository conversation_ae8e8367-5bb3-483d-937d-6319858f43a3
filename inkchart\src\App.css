/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: white;
  background-color: #151559;
}

.app {
  min-height: 100vh;
  background-color: #151559;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Main Content */
.main-content {
  padding: 60px 0;
  background-color: #151559;
  min-height: 100vh;
}

.bootcamp-section {
  max-width: 900px;
  margin: 0 auto;
}

.main-title {
  font-size: 3rem;
  color: #5FE785;
  text-align: center;
  margin-bottom: 50px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.content-block {
  margin-bottom: 40px;
  padding: 35px;
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.05) 0%, rgba(95, 231, 133, 0.02) 100%);
  border-radius: 20px;
  border: 2px solid rgba(95, 231, 133, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.content-block:hover {
  border-color: rgba(95, 231, 133, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(95, 231, 133, 0.1);
}

.content-title {
  font-size: 2rem;
  color: #5FE785;
  margin-bottom: 25px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.content-text {
  font-size: 1.15rem;
  line-height: 1.8;
  margin-bottom: 20px;
  color: white;
  opacity: 0.95;
}

.content-list {
  margin: 25px 0;
  padding-left: 25px;
}

.content-list li {
  font-size: 1.15rem;
  line-height: 1.8;
  margin-bottom: 15px;
  color: white;
  opacity: 0.95;
  position: relative;
}

.content-list li::marker {
  color: #5FE785;
}

.sub-list {
  margin-top: 15px;
  padding-left: 25px;
}

.sub-list li {
  margin-bottom: 8px;
  font-size: 1.05rem;
}

.sub-list li::marker {
  color: #5FE785;
}

.highlight {
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  padding: 25px;
  border-radius: 15px;
  font-weight: 600;
  font-size: 1.2rem;
  text-align: center;
  margin-top: 30px;
  box-shadow: 0 8px 25px rgba(95, 231, 133, 0.3);
  border: none;
}

/* Sessions Table */
.sessions-table {
  margin: 25px 0;
  border-radius: 15px;
  overflow: hidden;
  border: 2px solid rgba(95, 231, 133, 0.3);
}

.table-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  font-weight: 700;
}

.table-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  background: rgba(95, 231, 133, 0.05);
  border-top: 1px solid rgba(95, 231, 133, 0.2);
}

.table-row:hover {
  background: rgba(95, 231, 133, 0.1);
}

.table-cell {
  padding: 15px;
  text-align: center;
  font-size: 1.1rem;
}

.session-name {
  font-weight: 600;
  color: #5FE785;
}

/* Tip Box */
.tip-box {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.15) 0%, rgba(95, 231, 133, 0.05) 100%);
  border: 2px solid rgba(95, 231, 133, 0.4);
  border-radius: 15px;
  padding: 20px;
  margin: 25px 0;
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.tip-icon {
  font-size: 1.5rem;
  flex-shrink: 0;
}

.tip-text {
  font-size: 1.1rem;
  line-height: 1.7;
  color: white;
  margin: 0;
  font-weight: 500;
}

/* Example Box */
.example-box {
  background: rgba(95, 231, 133, 0.1);
  border-left: 4px solid #5FE785;
  border-radius: 10px;
  padding: 20px;
  margin: 20px 0;
}

.example-title {
  color: #5FE785;
  font-size: 1.2rem;
  margin-bottom: 10px;
  font-weight: 600;
}

.example-text {
  color: white;
  font-size: 1.1rem;
  margin: 0;
  font-family: 'Courier New', monospace;
  background: rgba(0, 0, 0, 0.3);
  padding: 10px;
  border-radius: 8px;
}

/* Formula Box */
.formula-box {
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  padding: 15px;
  border-radius: 10px;
  text-align: center;
  margin: 20px 0;
  box-shadow: 0 4px 15px rgba(95, 231, 133, 0.3);
}

.formula-text {
  font-size: 1.3rem;
  margin: 0;
  font-weight: 700;
}

/* Calculation Box */
.calculation-box {
  background: rgba(95, 231, 133, 0.08);
  border: 2px solid rgba(95, 231, 133, 0.3);
  border-radius: 15px;
  padding: 25px;
  margin: 25px 0;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.calc-icon {
  font-size: 2rem;
  flex-shrink: 0;
}

.calc-content {
  flex: 1;
}

.calc-title {
  color: #5FE785;
  font-size: 1.2rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.calc-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.calc-list li {
  color: white;
  font-size: 1.1rem;
  padding: 8px 0;
  padding-left: 20px;
  position: relative;
}

.calc-list li::before {
  content: "●";
  color: #5FE785;
  position: absolute;
  left: 0;
  font-size: 1.2rem;
}

/* Pairs Grid */
.pairs-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin: 25px 0;
}

.pair-item {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.2) 0%, rgba(95, 231, 133, 0.1) 100%);
  border: 2px solid rgba(95, 231, 133, 0.4);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  font-size: 1.3rem;
  font-weight: 700;
  color: #5FE785;
  transition: all 0.3s ease;
  cursor: pointer;
}

.pair-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(95, 231, 133, 0.3);
  border-color: #5FE785;
}

/* Favorite Pair */
.favorite-pair {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.15) 0%, rgba(95, 231, 133, 0.05) 100%);
  border: 3px solid #5FE785;
  border-radius: 20px;
  padding: 30px;
  margin-top: 30px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.favorite-pair::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(45deg, transparent, rgba(95, 231, 133, 0.1), transparent);
  animation: shimmer 3s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
  100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.favorite-title {
  color: white;
  font-size: 1.4rem;
  margin-bottom: 15px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.pair-highlight {
  color: #5FE785;
  font-size: 1.6rem;
  font-weight: 800;
  text-shadow: 0 0 10px rgba(95, 231, 133, 0.5);
}

.favorite-text {
  color: white;
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  position: relative;
  z-index: 1;
}

/* Definition Grid */
.definition-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 15px;
  margin: 25px 0;
}

.definition-item {
  display: flex;
  align-items: center;
  gap: 15px;
  background: rgba(95, 231, 133, 0.1);
  padding: 20px;
  border-radius: 12px;
  border-left: 4px solid #5FE785;
}

.definition-symbol {
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  padding: 10px 15px;
  border-radius: 8px;
  font-weight: 800;
  font-size: 1.2rem;
  min-width: 60px;
  text-align: center;
}

.definition-text {
  color: white;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Conversion Box */
.conversion-box {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.2) 0%, rgba(95, 231, 133, 0.1) 100%);
  border: 2px solid #5FE785;
  border-radius: 15px;
  padding: 20px;
  text-align: center;
  margin: 20px 0;
}

.conversion-text {
  color: #5FE785;
  font-size: 1.4rem;
  font-weight: 700;
  margin: 0;
  font-family: 'Courier New', monospace;
}

/* Benefits Grid */
.benefits-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
  margin: 30px 0;
}

.benefit-card {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.15) 0%, rgba(95, 231, 133, 0.05) 100%);
  border: 2px solid rgba(95, 231, 133, 0.3);
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.benefit-card:hover {
  transform: translateY(-5px);
  border-color: #5FE785;
  box-shadow: 0 10px 30px rgba(95, 231, 133, 0.2);
}

.benefit-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(95, 231, 133, 0.1), transparent);
  transition: left 0.5s ease;
}

.benefit-card:hover::before {
  left: 100%;
}

.benefit-title {
  color: #5FE785;
  font-size: 1.3rem;
  margin-bottom: 15px;
  font-weight: 600;
  position: relative;
  z-index: 1;
}

.benefit-desc {
  color: white;
  font-size: 1rem;
  margin: 0;
  opacity: 0.9;
  position: relative;
  z-index: 1;
  line-height: 1.6;
}

/* Rule Breakdown */
.rule-breakdown {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
  margin: 30px 0;
}

.rule-item {
  display: flex;
  align-items: center;
  gap: 25px;
  background: rgba(95, 231, 133, 0.08);
  border: 2px solid rgba(95, 231, 133, 0.3);
  border-radius: 15px;
  padding: 25px;
  transition: all 0.3s ease;
}

.rule-item:hover {
  border-color: #5FE785;
  background: rgba(95, 231, 133, 0.12);
}

.rule-number {
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 800;
  flex-shrink: 0;
  box-shadow: 0 4px 15px rgba(95, 231, 133, 0.3);
}

.rule-content {
  flex: 1;
}

.rule-title {
  color: #5FE785;
  font-size: 1.3rem;
  margin-bottom: 8px;
  font-weight: 600;
}

.rule-desc {
  color: white;
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
}

/* Process Box */
.process-box {
  background: rgba(95, 231, 133, 0.1);
  border: 2px solid rgba(95, 231, 133, 0.4);
  border-radius: 15px;
  padding: 25px;
  margin: 25px 0;
  display: flex;
  align-items: flex-start;
  gap: 20px;
}

.process-icon {
  font-size: 2.5rem;
  flex-shrink: 0;
}

.process-content {
  flex: 1;
}

.process-title {
  color: #5FE785;
  font-size: 1.4rem;
  margin-bottom: 15px;
  font-weight: 600;
}

.process-list {
  list-style: none;
  padding: 0;
  margin: 0;
  counter-reset: step-counter;
}

.process-list li {
  color: white;
  font-size: 1.1rem;
  line-height: 1.7;
  margin-bottom: 15px;
  padding-left: 40px;
  position: relative;
  counter-increment: step-counter;
}

.process-list li::before {
  content: counter(step-counter);
  position: absolute;
  left: 0;
  top: 0;
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.9rem;
  font-weight: 700;
}

/* Prop Firms Section */
.prop-firms-section {
  margin: 30px 0;
}

.firms-title {
  color: #5FE785;
  font-size: 1.3rem;
  margin-bottom: 20px;
  font-weight: 600;
  text-align: center;
}

.firms-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.firm-item {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.2) 0%, rgba(95, 231, 133, 0.1) 100%);
  border: 2px solid rgba(95, 231, 133, 0.4);
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
  transition: all 0.3s ease;
  cursor: pointer;
}

.firm-item:hover {
  transform: translateY(-3px);
  border-color: #5FE785;
  box-shadow: 0 8px 25px rgba(95, 231, 133, 0.3);
  color: #5FE785;
}

/* Transition Section */
.transition-section {
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.15) 0%, rgba(95, 231, 133, 0.05) 100%);
  border: 3px solid #5FE785;
  border-radius: 20px;
  padding: 40px;
  margin: 50px 0;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.transition-section::before {
  content: "";
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(45deg, #5FE785, transparent, #5FE785, transparent, #5FE785);
  border-radius: 20px;
  z-index: -1;
  animation: borderGlow 3s linear infinite;
}

@keyframes borderGlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.transition-text {
  color: white;
  font-size: 1.3rem;
  font-weight: 600;
  margin: 0;
  line-height: 1.6;
  position: relative;
  z-index: 1;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.2rem;
  }

  .content-block {
    padding: 25px;
  }

  .content-title {
    font-size: 1.6rem;
  }

  .content-text {
    font-size: 1.05rem;
  }

  .content-list li {
    font-size: 1.05rem;
  }

  .container {
    padding: 0 15px;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    text-align: left;
  }

  .table-cell {
    padding: 10px 15px;
    border-bottom: 1px solid rgba(95, 231, 133, 0.2);
  }

  .pairs-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 10px;
  }

  .pair-item {
    padding: 15px;
    font-size: 1.1rem;
  }

  .calculation-box {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .tip-box {
    flex-direction: column;
    gap: 10px;
    text-align: center;
  }

  .benefits-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .rule-item {
    flex-direction: column;
    text-align: center;
    gap: 15px;
  }

  .rule-number {
    width: 50px;
    height: 50px;
    font-size: 1.5rem;
  }

  .process-box {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }

  .firms-list {
    grid-template-columns: 1fr;
    gap: 10px;
  }

  .definition-item {
    flex-direction: column;
    text-align: center;
    gap: 10px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.8rem;
  }

  .content-block {
    padding: 20px;
  }

  .content-title {
    font-size: 1.4rem;
  }

  .content-text {
    font-size: 1rem;
  }

  .content-list li {
    font-size: 1rem;
  }

  .pairs-grid {
    grid-template-columns: 1fr;
  }

  .pair-item {
    padding: 12px;
    font-size: 1rem;
  }

  .favorite-title {
    font-size: 1.2rem;
  }

  .pair-highlight {
    font-size: 1.4rem;
  }

  .example-text {
    font-size: 0.95rem;
  }

  .formula-text {
    font-size: 1.1rem;
  }

  .benefit-card {
    padding: 20px;
  }

  .benefit-title {
    font-size: 1.1rem;
  }

  .rule-number {
    width: 45px;
    height: 45px;
    font-size: 1.3rem;
  }

  .rule-title {
    font-size: 1.1rem;
  }

  .process-title {
    font-size: 1.2rem;
  }

  .firms-title {
    font-size: 1.1rem;
  }

  .firm-item {
    padding: 15px;
    font-size: 1rem;
  }

  .transition-text {
    font-size: 1.1rem;
  }

  .definition-symbol {
    font-size: 1rem;
    padding: 8px 12px;
  }

  .conversion-text {
    font-size: 1.2rem;
  }
}
