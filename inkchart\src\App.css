/* Reset and base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: white;
  background-color: #151559;
}

.app {
  min-height: 100vh;
  background-color: #151559;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* Main Content */
.main-content {
  padding: 60px 0;
  background-color: #151559;
  min-height: 100vh;
}

.bootcamp-section {
  max-width: 900px;
  margin: 0 auto;
}

.main-title {
  font-size: 3rem;
  color: #5FE785;
  text-align: center;
  margin-bottom: 50px;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.content-block {
  margin-bottom: 40px;
  padding: 35px;
  background: linear-gradient(135deg, rgba(95, 231, 133, 0.05) 0%, rgba(95, 231, 133, 0.02) 100%);
  border-radius: 20px;
  border: 2px solid rgba(95, 231, 133, 0.2);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.content-block:hover {
  border-color: rgba(95, 231, 133, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 10px 30px rgba(95, 231, 133, 0.1);
}

.content-title {
  font-size: 2rem;
  color: #5FE785;
  margin-bottom: 25px;
  font-weight: 600;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.content-text {
  font-size: 1.15rem;
  line-height: 1.8;
  margin-bottom: 20px;
  color: white;
  opacity: 0.95;
}

.content-list {
  margin: 25px 0;
  padding-left: 25px;
}

.content-list li {
  font-size: 1.15rem;
  line-height: 1.8;
  margin-bottom: 15px;
  color: white;
  opacity: 0.95;
  position: relative;
}

.content-list li::marker {
  color: #5FE785;
}

.sub-list {
  margin-top: 15px;
  padding-left: 25px;
}

.sub-list li {
  margin-bottom: 8px;
  font-size: 1.05rem;
}

.sub-list li::marker {
  color: #5FE785;
}

.highlight {
  background: linear-gradient(135deg, #5FE785 0%, #4fd670 100%);
  color: #151559;
  padding: 25px;
  border-radius: 15px;
  font-weight: 600;
  font-size: 1.2rem;
  text-align: center;
  margin-top: 30px;
  box-shadow: 0 8px 25px rgba(95, 231, 133, 0.3);
  border: none;
}

/* Responsive Design */
@media (max-width: 768px) {
  .main-title {
    font-size: 2.2rem;
  }

  .content-block {
    padding: 25px;
  }

  .content-title {
    font-size: 1.6rem;
  }

  .content-text {
    font-size: 1.05rem;
  }

  .content-list li {
    font-size: 1.05rem;
  }

  .container {
    padding: 0 15px;
  }
}

@media (max-width: 480px) {
  .main-title {
    font-size: 1.8rem;
  }

  .content-block {
    padding: 20px;
  }

  .content-title {
    font-size: 1.4rem;
  }

  .content-text {
    font-size: 1rem;
  }

  .content-list li {
    font-size: 1rem;
  }
}
