{"version": 3, "names": ["_toBlock", "require", "ensureBlock", "node", "key", "result", "toBlock"], "sources": ["../../src/converters/ensureBlock.ts"], "sourcesContent": ["import toBlock from \"./toBlock.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Ensure the `key` (defaults to \"body\") of a `node` is a block.\n * Casting it to a block if it is not.\n *\n * Returns the BlockStatement\n */\nexport default function ensureBlock(\n  node: t.Node,\n  key: string = \"body\",\n): t.BlockStatement {\n  // @ts-expect-error Fixme: key may not exist in node, consider remove key = \"body\"\n  const result = toBlock(node[key], node);\n  // @ts-expect-error Fixme: key may not exist in node, consider remove key = \"body\"\n  node[key] = result;\n  return result;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,OAAA;AASe,SAASC,WAAWA,CACjCC,IAAY,EACZC,GAAW,GAAG,MAAM,EACF;EAElB,MAAMC,MAAM,GAAG,IAAAC,gBAAO,EAACH,IAAI,CAACC,GAAG,CAAC,EAAED,IAAI,CAAC;EAEvCA,IAAI,CAACC,GAAG,CAAC,GAAGC,MAAM;EAClB,OAAOA,MAAM;AACf", "ignoreList": []}