import './App.css'

function App() {
  return (
    <div className="app">
      <main className="main-content">
        <div className="container">
          <section className="bootcamp-section">
            <h1 className="main-title">Forex Bootcamp – Day 1</h1>

            <div className="content-block">
              <h2 className="content-title">Welcome & Intro</h2>
              <p className="content-text">
                Welcome to the Forex Bootcamp! I'm <PERSON><PERSON><PERSON><PERSON> from Stockink — and for the next 2
                days, my only goal is to give you clarity and confidence in trading. Not theory, not
                hype — just practical, real-world skills.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">1. What is the Forex Market?</h2>
              <p className="content-text">
                Let's begin with a basic but powerful question — What exactly is the Forex
                Market?
              </p>
              <p className="content-text">
                Forex stands for Foreign Exchange. It's where currencies are exchanged —
                simple. But the volume? Massive. Over $7 trillion dollars traded daily.
              </p>

              <ul className="content-list">
                <li>It's a 24-hour market — no bell, no opening or closing like stocks.</li>
                <li>It's decentralized — that means no single exchange controls it. It runs
                through banks, brokers, institutions, retail traders like you and me.</li>
                <li>Why do people trade Forex?
                  <ul className="sub-list">
                    <li>Central banks</li>
                    <li>Businesses (like Apple paying suppliers in yen)</li>
                    <li>And of course — retail traders for profit</li>
                  </ul>
                </li>
              </ul>

              <p className="content-text highlight">
                But without proper knowledge, it's just gambling. That's why you're here —
                to trade with skill and structure.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">2. Forex Market Timings (Sessions)</h2>
              <p className="content-text">
                Forex works in 4 major trading sessions — based on global time zones.
              </p>

              <div className="sessions-table">
                <div className="table-header">
                  <div className="table-cell">Session</div>
                  <div className="table-cell">IST Timing</div>
                  <div className="table-cell">Key Currencies</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">Sydney</div>
                  <div className="table-cell">3:30 AM – 12:30 PM</div>
                  <div className="table-cell">AUD, NZD</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">Tokyo</div>
                  <div className="table-cell">5:30 AM – 2:30 PM</div>
                  <div className="table-cell">JPY, USD</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">London</div>
                  <div className="table-cell">12:30 PM – 9:30 PM</div>
                  <div className="table-cell">GBP, EUR</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">New York</div>
                  <div className="table-cell">6:00 PM – 3:00 AM</div>
                  <div className="table-cell">USD, Gold, CAD</div>
                </div>
              </div>

              <div className="tip-box">
                <span className="tip-icon">💡</span>
                <p className="tip-text">
                  Most powerful time = London + New York Overlap (6 PM – 9:30 PM IST). That's
                  when we get strong moves — perfect for scalping or day trading.
                </p>
              </div>

              <p className="content-text">
                If you're serious about entries and sniper setups, this session is where you should
                be active.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">3. Pips & Pipettes Explained</h2>
              <p className="content-text">
                Next: a term you'll hear every day — Pips.
              </p>
              <p className="content-text">
                A pip is the smallest unit of price change in Forex.
              </p>

              <div className="example-box">
                <h4 className="example-title">Example:</h4>
                <p className="example-text">
                  If EUR/USD moves from 1.1050 → 1.1051, that's 1 pip.
                </p>
              </div>

              <p className="content-text">
                But brokers also show prices with 5 digits — that 5th digit is called a pipette (1/10
                of a pip).
              </p>

              <div className="formula-box">
                <p className="formula-text">So: <strong>1 pip = 10 pipettes</strong></p>
              </div>

              <div className="calculation-box">
                <span className="calc-icon">🔢</span>
                <div className="calc-content">
                  <p className="calc-title">These small movements are how we calculate:</p>
                  <ul className="calc-list">
                    <li>Profit/Loss</li>
                    <li>Lot size</li>
                    <li>Risk per trade</li>
                  </ul>
                </div>
              </div>

              <p className="content-text">
                So when I say, 'My SL is 20 pips,' — now you know what I mean.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">4. Major Forex Pairs</h2>
              <p className="content-text">
                There are hundreds of pairs, but you don't need all of them.
              </p>
              <p className="content-text">
                Stick to the majors — most liquid, tight spreads, clear structure.
              </p>

              <div className="pairs-grid">
                <div className="pair-item">EUR/USD</div>
                <div className="pair-item">GBP/USD</div>
                <div className="pair-item">USD/JPY</div>
                <div className="pair-item">USD/CHF</div>
                <div className="pair-item">AUD/USD</div>
                <div className="pair-item">USD/CAD</div>
              </div>

              <div className="favorite-pair">
                <h4 className="favorite-title">And our favorite: <span className="pair-highlight">XAU/USD</span> — Gold vs Dollar</h4>
                <p className="favorite-text">
                  It's volatile but rewarding — and we'll go deep into it tomorrow
                </p>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  )
}

export default App
