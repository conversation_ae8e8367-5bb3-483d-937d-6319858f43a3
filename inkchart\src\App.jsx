import './App.css'

function App() {
  return (
    <div className="app">
      <main className="main-content">
        <div className="container">
          <section className="bootcamp-section">
            <h1 className="main-title">Forex Bootcamp – Day 1</h1>

            <div className="content-block">
              <h2 className="content-title">Welcome & Intro</h2>
              <p className="content-text">
                Welcome to the Forex Bootcamp! I'm <PERSON><PERSON><PERSON><PERSON> from Stockink — and for the next 2
                days, my only goal is to give you clarity and confidence in trading. Not theory, not
                hype — just practical, real-world skills.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">1. What is the Forex Market?</h2>
              <p className="content-text">
                Let's begin with a basic but powerful question — What exactly is the Forex
                Market?
              </p>
              <p className="content-text">
                Forex stands for Foreign Exchange. It's where currencies are exchanged —
                simple. But the volume? Massive. Over $7 trillion dollars traded daily.
              </p>

              <ul className="content-list">
                <li>It's a 24-hour market — no bell, no opening or closing like stocks.</li>
                <li>It's decentralized — that means no single exchange controls it. It runs
                through banks, brokers, institutions, retail traders like you and me.</li>
                <li>Why do people trade Forex?
                  <ul className="sub-list">
                    <li>Central banks</li>
                    <li>Businesses (like Apple paying suppliers in yen)</li>
                    <li>And of course — retail traders for profit</li>
                  </ul>
                </li>
              </ul>

              <p className="content-text highlight">
                But without proper knowledge, it's just gambling. That's why you're here —
                to trade with skill and structure.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">2. Forex Market Timings (Sessions)</h2>
              <p className="content-text">
                Forex works in 4 major trading sessions — based on global time zones.
              </p>

              <div className="sessions-table">
                <div className="table-header">
                  <div className="table-cell">Session</div>
                  <div className="table-cell">IST Timing</div>
                  <div className="table-cell">Key Currencies</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">Sydney</div>
                  <div className="table-cell">3:30 AM – 12:30 PM</div>
                  <div className="table-cell">AUD, NZD</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">Tokyo</div>
                  <div className="table-cell">5:30 AM – 2:30 PM</div>
                  <div className="table-cell">JPY, USD</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">London</div>
                  <div className="table-cell">12:30 PM – 9:30 PM</div>
                  <div className="table-cell">GBP, EUR</div>
                </div>
                <div className="table-row">
                  <div className="table-cell session-name">New York</div>
                  <div className="table-cell">6:00 PM – 3:00 AM</div>
                  <div className="table-cell">USD, Gold, CAD</div>
                </div>
              </div>

              <div className="tip-box">
                <span className="tip-icon">💡</span>
                <p className="tip-text">
                  Most powerful time = London + New York Overlap (6 PM – 9:30 PM IST). That's
                  when we get strong moves — perfect for scalping or day trading.
                </p>
              </div>

              <p className="content-text">
                If you're serious about entries and sniper setups, this session is where you should
                be active.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">3. Pips & Pipettes Explained</h2>
              <p className="content-text">
                Next: a term you'll hear every day — Pips.
              </p>
              <p className="content-text">
                A pip is the smallest unit of price change in Forex.
              </p>

              <div className="example-box">
                <h4 className="example-title">Example:</h4>
                <p className="example-text">
                  If EUR/USD moves from 1.1050 → 1.1051, that's 1 pip.
                </p>
              </div>

              <p className="content-text">
                But brokers also show prices with 5 digits — that 5th digit is called a pipette (1/10
                of a pip).
              </p>

              <div className="formula-box">
                <p className="formula-text">So: <strong>1 pip = 10 pipettes</strong></p>
              </div>

              <div className="calculation-box">
                <span className="calc-icon">🔢</span>
                <div className="calc-content">
                  <p className="calc-title">These small movements are how we calculate:</p>
                  <ul className="calc-list">
                    <li>Profit/Loss</li>
                    <li>Lot size</li>
                    <li>Risk per trade</li>
                  </ul>
                </div>
              </div>

              <p className="content-text">
                So when I say, 'My SL is 20 pips,' — now you know what I mean.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">4. Major Forex Pairs</h2>
              <p className="content-text">
                There are hundreds of pairs, but you don't need all of them.
              </p>
              <p className="content-text">
                Stick to the majors — most liquid, tight spreads, clear structure.
              </p>

              <div className="pairs-grid">
                <div className="pair-item">EUR/USD</div>
                <div className="pair-item">GBP/USD</div>
                <div className="pair-item">USD/JPY</div>
                <div className="pair-item">USD/CHF</div>
                <div className="pair-item">AUD/USD</div>
                <div className="pair-item">USD/CAD</div>
              </div>

              <div className="favorite-pair">
                <h4 className="favorite-title">And our favorite: <span className="pair-highlight">XAU/USD</span> — Gold vs Dollar</h4>
                <p className="favorite-text">
                  It's volatile but rewarding — and we'll go deep into it tomorrow
                </p>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">What is XAU/USD?</h2>
              <p className="content-text">
                XAU/USD is the Forex symbol for Gold priced in US Dollars.
              </p>

              <div className="definition-grid">
                <div className="definition-item">
                  <span className="definition-symbol">XAU</span>
                  <span className="definition-text">= international symbol for 1 ounce of Gold</span>
                </div>
                <div className="definition-item">
                  <span className="definition-symbol">USD</span>
                  <span className="definition-text">= United States Dollar</span>
                </div>
              </div>

              <div className="conversion-box">
                <p className="conversion-text">1 ounce = 28.3495</p>
              </div>

              <div className="example-box">
                <p className="example-text">
                  So, when you see XAU/USD = 2350, it means 1 ounce of Gold is worth 2350 USD.
                </p>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">Why Trade Gold (XAU/USD)?</h2>
              <p className="content-text">
                Gold is not just a commodity — it acts like a safe-haven currency.
              </p>

              <div className="benefits-grid">
                <div className="benefit-card">
                  <h4 className="benefit-title">High Volatility</h4>
                  <p className="benefit-desc">Big moves = More trading opportunities</p>
                </div>
                <div className="benefit-card">
                  <h4 className="benefit-title">Clear Technical Structure</h4>
                  <p className="benefit-desc">Great for price action traders</p>
                </div>
                <div className="benefit-card">
                  <h4 className="benefit-title">Follows Global Sentiment</h4>
                  <p className="benefit-desc">Moves on inflation, war, USD strength/weakness</p>
                </div>
                <div className="benefit-card">
                  <h4 className="benefit-title">24/5 Liquidity</h4>
                  <p className="benefit-desc">Easy execution even with large volume</p>
                </div>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">What is the 5-3-1 rule in forex?</h2>
              <p className="content-text">
                The strategy is named after its three key components: 5 (five currency pairs to focus
                on), 3 (only three trading strategies), and 1 (pick one time to trade). The main goal
                of this forex algo trading strategy is to maintain consistency, minimize risks, and
                maximize the traders' overall returns.
              </p>

              <div className="rule-breakdown">
                <div className="rule-item">
                  <div className="rule-number">5</div>
                  <div className="rule-content">
                    <h4 className="rule-title">Currency Pairs</h4>
                    <p className="rule-desc">Focus on five currency pairs only</p>
                  </div>
                </div>
                <div className="rule-item">
                  <div className="rule-number">3</div>
                  <div className="rule-content">
                    <h4 className="rule-title">Trading Strategies</h4>
                    <p className="rule-desc">Master only three trading strategies</p>
                  </div>
                </div>
                <div className="rule-item">
                  <div className="rule-number">1</div>
                  <div className="rule-content">
                    <h4 className="rule-title">Trading Time</h4>
                    <p className="rule-desc">Pick one specific time to trade</p>
                  </div>
                </div>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">7. What is a Funded Account?</h2>
              <p className="content-text">
                Now this is for those of you thinking — I have the skills but not enough capital.
              </p>
              <p className="content-text">
                Here's the answer — Prop Firms and Funded Accounts.
              </p>
              <p className="content-text">
                These companies give you capital — $10,000 to $200,000+ — and you keep a
                70–90% profit share.
              </p>

              <div className="process-box">
                <span className="process-icon">🧪</span>
                <div className="process-content">
                  <h4 className="process-title">How?</h4>
                  <ol className="process-list">
                    <li>You pass their evaluation challenge (hit a profit target without violating rules).</li>
                    <li>You prove you can manage risk, avoid overtrading, stay disciplined.</li>
                    <li>If passed, you're funded.</li>
                  </ol>
                </div>
              </div>

              <div className="prop-firms-section">
                <h4 className="firms-title">Top Prop Firms:</h4>
                <div className="firms-list">
                  <div className="firm-item">Funding pips</div>
                  <div className="firm-item">Alpha capita</div>
                  <div className="firm-item">The5ers</div>
                </div>
              </div>

              <p className="content-text highlight">
                It's the fastest way to scale without risking your own money — but it
                demands pure discipline.
              </p>
            </div>

            <div className="transition-section">
              <p className="transition-text">
                Now that you've understood the basics of the Forex market — let's talk
                about something that can make or break your trading style
              </p>
            </div>

            <div className="content-block">
              <div className="trading-styles-intro">
                <p className="intro-text">
                  Every trader eventually picks a side (consciously or not):
                </p>

                <div className="styles-comparison">
                  <div className="style-card pa-card">
                    <div className="style-icon">🔹</div>
                    <div className="style-content">
                      <h4 className="style-name">Price Action (PA)</h4>
                      <p className="style-desc">classic, clean, candle-based</p>
                    </div>
                  </div>

                  <div className="vs-divider">VS</div>

                  <div className="style-card smc-card">
                    <div className="style-icon">🔹</div>
                    <div className="style-content">
                      <h4 className="style-name">Smart Money Concepts (SMC)</h4>
                      <p className="style-desc">structure, manipulation, liquidity</p>
                    </div>
                  </div>
                </div>

                <div className="warning-box">
                  <p className="warning-text">
                    Both can work — but you must know the difference.
                  </p>
                  <p className="warning-text">
                    Because using both at the wrong time is like mixing two languages — you'll
                    confuse the market and yourself.
                  </p>
                </div>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">Definition: Price Action Trading</h2>
              <p className="content-text">
                Price Action Trading is a trading method that involves reading and analyzing
                the movement of price on a chart — without relying on indicators.
              </p>
              <p className="content-text">
                It focuses on how price behaves at key levels (support, resistance, trendlines,
                zones) using candlestick patterns, breakouts, and market structure to make
                trading decisions.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">Definition: Smart Money Concept (SMC)</h2>
              <p className="content-text">
                Smart Money Concept (SMC) is an advanced price action-based trading method that
                focuses on how big institutions (banks, hedge funds) move the market.
              </p>
              <p className="content-text">
                SMC traders try to follow the "smart money" by identifying their footprints — like
                liquidity grabs, order blocks, market structure shifts, and mitigation zones — to
                enter high-probability trades.
              </p>
            </div>

            <div className="content-block">
              <h2 className="content-title">Candlestick Basics & Trendline Mastery</h2>

              <div className="candlestick-section">
                <h3 className="subsection-title">Basic Structure:</h3>
                <p className="content-text">
                  Each candle shows price movement within a specific time (e.g., 1H candle = 1 hour
                  of price data):
                </p>

                <div className="candle-components">
                  <div className="component-item">
                    <div className="component-dot"></div>
                    <div className="component-text">
                      <strong>Open</strong> – Where price started
                    </div>
                  </div>
                  <div className="component-item">
                    <div className="component-dot"></div>
                    <div className="component-text">
                      <strong>Close</strong> – Where price ended
                    </div>
                  </div>
                  <div className="component-item">
                    <div className="component-dot"></div>
                    <div className="component-text">
                      <strong>Wick (or Shadow)</strong> – Highs and lows of that session
                    </div>
                  </div>
                  <div className="component-item">
                    <div className="component-dot"></div>
                    <div className="component-text">
                      <strong>Body</strong> – Difference between open and close
                    </div>
                  </div>
                </div>

                <div className="candle-types">
                  <div className="candle-type bullish">
                    <div className="candle-color"></div>
                    <div className="candle-info">
                      <h4>Bullish</h4>
                      <p>Green colour - Uptrend</p>
                    </div>
                  </div>
                  <div className="candle-type bearish">
                    <div className="candle-color"></div>
                    <div className="candle-info">
                      <h4>Bearish</h4>
                      <p>Red colour - Downtrend</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">Explain the time frame</h2>
              <p className="content-text">
                When you open a chart, every candle you see represents a certain time
                duration — that's called a timeframe.
              </p>

              <div className="timeframes-section">
                <h3 className="subsection-title">Types of Traders & Their Timeframes</h3>

                <div className="traders-table">
                  <div className="table-header">
                    <div className="table-cell">Trader Type</div>
                    <div className="table-cell">Timeframes Used</div>
                    <div className="table-cell">Holding Period</div>
                  </div>
                  <div className="table-row">
                    <div className="table-cell trader-type">Scalper</div>
                    <div className="table-cell">1M, 5M, 15M</div>
                    <div className="table-cell">Seconds to minutes</div>
                  </div>
                  <div className="table-row">
                    <div className="table-cell trader-type">Day Trader</div>
                    <div className="table-cell">5M, 15M, 1H</div>
                    <div className="table-cell">Same day</div>
                  </div>
                  <div className="table-row">
                    <div className="table-cell trader-type">Swing Trader</div>
                    <div className="table-cell">1H, 4H, Daily</div>
                    <div className="table-cell">Days to weeks</div>
                  </div>
                  <div className="table-row">
                    <div className="table-cell trader-type">Position Trader</div>
                    <div className="table-cell">Daily, Weekly</div>
                    <div className="table-cell">Weeks to months</div>
                  </div>
                </div>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">Types of candle</h2>

              <div className="patterns-table">
                <div className="table-header">
                  <div className="table-cell">Pattern</div>
                  <div className="table-cell">Meaning</div>
                </div>
                <div className="table-row">
                  <div className="table-cell pattern-name">Pin Bar / Hammer</div>
                  <div className="table-cell">Bullish reversal — buyers defended lows</div>
                </div>
                <div className="table-row">
                  <div className="table-cell pattern-name">Shooting Star</div>
                  <div className="table-cell">Bearish reversal — sellers rejected highs</div>
                </div>
                <div className="table-row">
                  <div className="table-cell pattern-name">Engulfing Candle</div>
                  <div className="table-cell">Strong reversal — body completely "eats" prior candle</div>
                </div>
                <div className="table-row">
                  <div className="table-cell pattern-name">Doji</div>
                  <div className="table-cell">Indecision — wait for confirmation</div>
                </div>
              </div>

              <div className="important-note">
                <span className="note-icon">👉</span>
                <p className="note-text">
                  Always combine candles with structure — don't trade them blindly.
                </p>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">Understanding Trendlines</h2>
              <p className="content-text quote">
                "A trendline is a simple line that helps you visually connect market structure
                — highs or lows — to spot trends."
              </p>

              <div className="trendline-section">
                <h3 className="subsection-title">Drawing Rules:</h3>
                <ul className="rules-list">
                  <li>Uptrend → Connect higher lows (support line below price)</li>
                  <li>Downtrend → Connect lower highs (resistance line above price)</li>
                  <li>At least 2–3 touches are needed to validate a trendline</li>
                  <li>Trendlines are not exact — think of them like zones</li>
                </ul>
              </div>

              <div className="trendline-section">
                <h3 className="subsection-title">When to Use:</h3>
                <ul className="usage-list">
                  <li>Identify trend direction</li>
                  <li>Enter on trendline bounce (continuation)</li>
                  <li>Spot breakouts and structure shifts (trend reversals)</li>
                </ul>
              </div>

              <div className="trendline-section">
                <h3 className="subsection-title">Common Mistakes:</h3>
                <ul className="mistakes-list">
                  <li>Forcing a trendline where it doesn't fit</li>
                  <li>Ignoring wicks and only drawing to body</li>
                </ul>
              </div>
            </div>

            <div className="content-block">
              <h2 className="content-title">Understanding Trendlines</h2>
              <p className="content-text quote">
                "A trendline is a simple line that helps you visually connect market structure —
                highs or lows — to spot trends."
              </p>

              <div className="trendline-section">
                <h3 className="subsection-title">Drawing Rules:</h3>
                <ul className="rules-list">
                  <li>Uptrend → Connect higher lows (support line below price)</li>
                  <li>Downtrend → Connect lower highs (resistance line above price)</li>
                  <li>At least 2–3 touches are needed to validate a trendline</li>
                  <li>Trendlines are not exact — think of them like zones</li>
                </ul>
              </div>

              <div className="trendline-section">
                <h3 className="subsection-title">When to Use:</h3>
                <ul className="usage-list">
                  <li>Identify trend direction</li>
                  <li>Enter on trendline bounce (continuation)</li>
                  <li>Spot breakouts and structure shifts (trend reversals)</li>
                </ul>
              </div>

              <div className="trendline-section">
                <h3 className="subsection-title">Common Mistakes:</h3>
                <ul className="mistakes-list">
                  <li>Forcing a trendline where it doesn't fit</li>
                  <li>Ignoring wicks and only drawing to body</li>
                </ul>
              </div>
            </div>
          </section>
        </div>
      </main>
    </div>
  )
}

export default App
